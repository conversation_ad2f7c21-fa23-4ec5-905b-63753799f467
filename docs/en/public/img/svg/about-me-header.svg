
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="z-index:1;position:relative" width="854" height="180" viewBox="0 0 854 180">
                <style>
                            .text {
						font-size: 30px;
						font-weight: 700;
						font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;
					}
					.desc {
						font-size: 20px;
						font-weight: 500;
						font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;
					}
                            .text, .desc {
							animation: twinkling 4s ease-in-out infinite;
						}@keyframes twinkling {
						  40% { opacity: 1; }
						  50% { opacity: 0.5; }
						  60% { opacity: 1; }
						  70% { opacity: 0.5; }
						  80% { opacity: 1; }
						};
                        </style>
                <g transform="translate(427, 90) scale(1, 1) translate(-427, -90)">
                    
                                
                                <path d="" fill="#40B883" opacity="0.4" >
                <animate attributeName="d" dur="20s" repeatCount="indefinite" keyTimes="0;0.333;0.667;1" calcmod="spline" keySplines="0.2 0 0.2 1;0.2 0 0.2 1;0.2 0 0.2 1" begin="0s" values="M0 0L 0 100Q 213.5 140 427 110T 854 135L 854 0 Z;M0 0L 0 125Q 213.5 140 427 120T 854 110L 854 0 Z;M0 0L 0 145Q 213.5 115 427 145T 854 110L 854 0 Z;M0 0L 0 100Q 213.5 140 427 110T 854 135L 854 0 Z"></animate>
            </path>
            <path d="" fill="#40B883" opacity="0.4" >
                <animate attributeName="d" dur="20s" repeatCount="indefinite" keyTimes="0;0.333;0.667;1" calcmod="spline" keySplines="0.2 0 0.2 1;0.2 0 0.2 1;0.2 0 0.2 1" begin="-10s" values="M0 0L 0 115Q 213.5 160 427 130T 854 140L 854 0 Z;M0 0L 0 130Q 213.5 100 427 100T 854 120L 854 0 Z;M0 0L 0 125Q 213.5 105 427 130T 854 145L 854 0 Z;M0 0L 0 115Q 213.5 160 427 130T 854 140L 854 0 Z"></animate>
            </path>
                </g>
                
                        <text text-anchor="middle" alignment-baseline="middle" x="50%" y="35%" class="text" style="fill:#fff;" stroke="#none" stroke-width="1" >关于我 (Se7en)</text>
                 
            </svg>
        