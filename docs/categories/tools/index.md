---
showArticleMetadata: false
editLink: false
lastUpdated: false
showComment: false
---

# 精选工具箱

## AI IDE

- [Cursor](https://docs.cursor.com/)：Cursor 是一款以 AI 为核心的高效代码编辑器，旨在优化开发工作流程。
- [Cline](https://github.com/cline/cline)：IDE 中的自动编程 Agent，能够在每一步都征得你同意的前提下创建/编辑文件、执行命令、使用浏览器等操作。
- [Roo Code](https://github.com/RooVetGit/Roo-Code)：Roo Code（原 Roo Cline）为你的代码编辑器配备了一支完整的 AI 开发团队。
- [Trae](https://www.trae.com.cn/)：Trae（/treɪ/）IDE 与 AI 深度集成，提供智能问答、代码自动补全以及基于 Agent 的 AI 自动编程能力。
- [Windsurf](https://windsurf.com/)：下一代 AI IDE，提供智能问答、代码自动补全以及基于 Agent 的 AI 自动编程能力。Windsurf 的 Cascade 模式最早支持直接对多个代码文件进行修改。

## Markdown

- [MarkItDown](https://github.com/microsoft/markitdown)：将文件和 Office 文档转换为 Markdown 格式。
- [URL too Markdown](https://urltoomarkdown.com/)：在线转换 URL 到 Markdown。

## Network

- [IPinfo](https://ipinfo.io/)：获取 IP 地理信息，包括国家、城市，邮编等，以及所属公司、ASN 等信息。