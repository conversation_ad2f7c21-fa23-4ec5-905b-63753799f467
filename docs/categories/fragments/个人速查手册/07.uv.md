---
title: uv 速查手册
author: Se7en
date: 2024/08/01 10:00
categories:
 - 个人速查手册
tags:
 - uv
 - python
---

## uv run 命令自动补全文件名

默认情况下 `uv run` 命令不会补全文件名，因为 `uv` 命令有其他子命令需要补全。相关 issue：https://github.com/astral-sh/uv/issues/8432

添加以下内容到 `~/.zshrc` 文件中：

```bash
eval "$(uv generate-shell-completion zsh)" # you should already have these two lines
eval "$(uvx --generate-shell-completion zsh)"

# you will need to add the lines below
# https://github.com/astral-sh/uv/issues/8432#issuecomment-2453494736
_uv_run_mod() {
    if [[ "$words[2]" == "run" && "$words[CURRENT]" != -* ]]; then
        _arguments '*:filename:_files'
    else
        _uv "$@"
    fi
}
compdef _uv_run_mod uv
```

然后执行 `uv run` 再按 `tab` 键即可补全文件名，如果要补全子命令，可以加上 `-` 再按 `tab` 键。
