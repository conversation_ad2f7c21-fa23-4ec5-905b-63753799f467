---
title: AI
author: Se7en
categories:
 - AI
tags:
 - AI
 - Open Source
---

# AI

## AI Agent

### smolagents

[smolagents](https://github.com/huggingface/smolagents) 是一个库，让你只需几行代码就能运行强大的智能代理。它提供了以下特点：

✨ **简单性**：代理的逻辑仅需约 1000 行代码（见 [agents.py](https://github.com/huggingface/smolagents/blob/main/src/smolagents/agents.py)）。我们将抽象层保持在最低限度，直接基于原始代码！

🧑‍💻 **对代码代理的一流支持**：我们的 [`CodeAgent`](https://huggingface.co/docs/smolagents/reference/agents#smolagents.CodeAgent) 通过编写代码来执行动作（而不是“代理被用来编写代码”）。为了确保安全性，我们支持通过 [E2B](https://e2b.dev/) 在沙箱环境中执行代码。

🤗 **Hub 集成**：你可以[从 Hub 分享或拉取工具](https://huggingface.co/docs/smolagents/reference/tools#smolagents.Tool.from_hub)，未来还会有更多功能！

🌐 **模型无关性**：smolagents 支持任何大语言模型（LLM）。可以是本地的 `transformers` 或 `ollama` 模型，也可以通过 [Hub 上的多种提供商](https://huggingface.co/blog/inference-providers)，或者通过我们的 [LiteLLM](https://www.litellm.ai/) 集成支持 OpenAI、Anthropic 等多种模型。

👁️ **多模态支持**：代理支持文本、视觉、视频甚至音频输入！请参阅[此教程](https://huggingface.co/docs/smolagents/examples/web_browser) 了解视觉相关内容。

🛠️ **工具无关性**：你可以使用 [LangChain](https://huggingface.co/docs/smolagents/reference/tools#smolagents.Tool.from_langchain)、[Anthropic 的 MCP](https://huggingface.co/docs/smolagents/reference/tools#smolagents.ToolCollection.from_mcp) 的工具，甚至可以将 [Hub Space](https://huggingface.co/docs/smolagents/reference/tools#smolagents.Tool.from_space) 用作工具。

以下是一个简单的使用示例：

```python
from smolagents import CodeAgent, HfApiModel

model_id = "meta-llama/Llama-3.3-70B-Instruct" 

model = HfApiModel(model_id=model_id, token="<YOUR_HUGGINGFACEHUB_API_TOKEN>") # You can choose to not pass any model_id to HfApiModel to use a default free model
# you can also specify a particular provider e.g. provider="together" or provider="sambanova"
agent = CodeAgent(tools=[], model=model, add_base_tools=True)

agent.run(
    "Could you give me the 118th number in the Fibonacci sequence?",
)
```

### autogen

AutoGen 是一个开源编程框架，用于构建 AI 代理并促进多个代理之间的合作以解决问题。AutoGen 旨在提供一个易于使用和灵活的框架，以加速代理型 AI 的开发和研究。它提供了诸如代理之间可以对话、LLM 和工具使用支持、自主和人机协作工作流以及多代理对话模式等功能。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202502231559347.png)

入门的例子是创建一个 AI 团队，一个当任务执行者，一个当评判者，当评判者觉得可以了，就停止对话，完成任务。

```python
import asyncio

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.conditions import ExternalTermination, TextMentionTermination
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import Console
from autogen_core import CancellationToken
from autogen_ext.models.openai import OpenAIChatCompletionClient

async def main():

    # Create an OpenAI model client.
    model_client = OpenAIChatCompletionClient(
        model="gpt-4o-2024-08-06",
        api_key="", # Optional if you have an OPENAI_API_KEY env variable set.
        )

    # Create the primary agent.
    primary_agent = AssistantAgent(
        "primary",
        model_client=model_client,
        system_message="You are a helpful AI assistant.",
    )

    # Create the critic agent.
    critic_agent = AssistantAgent(
        "critic",
        model_client=model_client,
        system_message="Provide constructive feedback. Respond with 'APPROVE' to when your feedbacks are addressed.",
    )

    # Define a termination condition that stops the task if the critic approves.
    text_termination = TextMentionTermination("APPROVE")


    team = RoundRobinGroupChat(
        [primary_agent, critic_agent],
        termination_condition=text_termination,  # Use the bitwise OR operator to combine conditions.
    )

    await Console(team.run_stream(task="写一首关于秋天的短诗"))

    
if __name__ == "__main__":
    asyncio.run(main())
```

输出结果如下：

```python
---------- user ----------
写一首关于秋天的短诗
---------- primary ----------
秋风轻摇叶漫天，  
金黄稻穗笑丰年。  
枫红染尽山川路，  
硕果满枝惹人怜。  

晨霜点缀草间珠，  
夕阳映影湖中船。  
静谧凉月盈满空，  
秋夜温柔如梦甜。  
[Prompt tokens: 28, Completion tokens: 81]
---------- critic ----------
这首关于秋天的短诗运用了丰富的意象生动地描绘了秋天的美丽。以下是一些建议来进一步提升这首诗：

1. 开头的两行已经相当完整，但可以考虑将“轻摇”替换为更具体的动词，比如“飘落”，以增强画面感。
2. 第二节中的“晨霜点缀草间珠”可以更加具体化，比如但不局限于使用“白霜”，进一步富有诗意地描述秋季清晨的细节。
3. 尾句“秋夜温柔如梦甜”可以再结合一个感官描写，比如“如梦”可以结合沁人心脾的“桂香扑鼻”之类的细节来丰富整体体验。

这些建议旨在增加诗歌的生动性和情感深度，整体上已经非常优秀。请进行相应的改动后，我将很高兴地给予批准。
[Prompt tokens: 126, Completion tokens: 215]
---------- primary ----------
非常感谢您的建议！根据您的意见，我对诗歌做了一些修改：

秋风飘落叶漫天，  
金黄稻穗笑丰年。  
枫红染尽山川路，  
硕果满枝惹人怜。  

白霜点缀草间珠，  
夕阳映影湖中船。  
桂香扑鼻月满空，  
秋夜温柔如梦甜。  

希望这些改动能够增强诗歌的生动性，带来更加丰富的秋季体验。
[Prompt tokens: 334, Completion tokens: 120]
---------- critic ----------
改动后的诗歌确实更加生动且富有画面感。使用“飘落”更好地描绘了秋天的特征，而“白霜”和“桂香扑鼻”的加入使得诗歌在感官描述上更加具体和丰富。这 些改动有效地增强了诗歌的生动性和情感深度。因此，我准许该稿件。 

APPROVE
[Prompt tokens: 471, Completion tokens: 92]
---------- Summary ----------
Number of messages: 5
Finish reason: Text 'APPROVE' mentioned
Total prompt tokens: 959
Total completion tokens: 508
Duration: 14.87 seconds
```

参考资料：
- [AutoGen入门-让两个AI自行聊天完成任务](https://www.cnblogs.com/mingupupu/p/18658018)

## AI 可观测

### Arize Phoenix

[Phoenix](https://github.com/Arize-ai/phoenix) 是一个开源的 AI 可观测平台，专为实验、评估和故障排除而设计。它提供：

- [**追踪（Tracing）**](https://docs.arize.com/phoenix/tracing/llm-traces) - 使用基于 OpenTelemetry 的工具对 LLM 应用程序的运行时进行追踪。  
- [**评估（Evaluation）**](https://docs.arize.com/phoenix/evaluation/llm-evals) - 利用 LLM 进行基准测试，通过响应和检索评估衡量应用程序的性能。  
- [**数据集（Datasets）**](https://docs.arize.com/phoenix/datasets-and-experiments/overview-datasets) - 创建版本化的数据集，以支持实验、评估和微调。  
- [**实验（Experiments）**](https://docs.arize.com/phoenix/datasets-and-experiments/overview-datasets#experiments) - 跟踪并评估提示词、LLM 和检索方式的变更影响。  
- [**游乐场（Playground）**](https://docs.arize.com/phoenix/prompt-engineering/overview-prompts) - 优化提示词、对比模型、调整参数，并重放追踪到的 LLM 调用。  

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202502162001391.gif)

## AI 代理

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202501032030701.png)

参考资料：

- [LLM供应商](https://www.xiaogenban1993.com/blog/24.09/llm%E4%BE%9B%E5%BA%94%E5%95%86)

### OpenRouter

[OpenRouter](https://openrouter.ai/) 是一个统一的 API 服务平台，旨在通过单一接口为用户提供对多种大型语言模型（LLM）的访问。 

该平台支持多种知名模型，包括 OpenAI 的 GPT-3.5 和 GPT-4、Anthropic 的 Claude 系列、Meta 的 LLaMA 系列、Google 的 Gemini 系列等。

闭源模型如 GPT 是和 [OpenAI 的官网价格](https://openai.com/api/pricing/)一致甚至更低，并且已经提供了最新的 o1 模型了。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202501032022759.png)

开源 llama3.1-70b 8K 上下文甚至免费，131K 上下文收费也仅有 0.12 美元/百万 token。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202501032022920.png)

**使用方法**

请求的时候统一指定 OpenRouter 的地址：https://openrouter.ai，在 model 中指定模型名称，例如 `openai/gpt-3.5-turbo`、`anthropic/claude-3.5-haiku-20241022`，请求头和请求体统一使用 OpenAI 的格式。

```bash
curl https://openrouter.ai/api/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${OPENROUTER_API_KEY}" \
  -d '{
  "model": "openai/gpt-3.5-turbo",
  "messages": [
    {
      "role": "user",
      "content": "What is the meaning of life?"
    }
  ]
}'


curl https://openrouter.ai/api/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${OPENROUTER_API_KEY}" \
  -d '{
  "model": "anthropic/claude-3.5-haiku-20241022",
  "messages": [
    {
      "role": "user",
      "content": "What is the meaning of life?"
    }
  ]
}'
```

## 其他

### Screenpipe

Screenpipe 是一款开源的 AI 工具，能够 24 小时不间断地记录您的屏幕和音频数据，并将其存储在本地数据库中。Screenpipe 通过结合大型语言模型（LLMs），可实现对您在电脑上所做事情的进行对话、总结和回顾。

参考资料：

- [Screenpipe use case examples](https://docs.screenpi.pe/docs/examples)
- [Screenpipe available pipes](https://docs.screenpi.pe/docs/plugins#available-pipes)