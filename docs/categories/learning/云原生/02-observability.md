---
title: Observability
author: Se7en
categories:
 - Observability
tags:
 - Observability
---


## 参考资料

- [Introduction to exemplars](https://grafana.com/docs/grafana/latest/fundamentals/exemplars/)
- [Configure a Tempo data source](https://grafana.com/docs/grafana/latest/datasources/tempo/configure-tempo-data-source/)
- [Correlate Your Metrics, Logs & Traces with the curated OSS observability stack from Grafana Labs](https://www.youtube.com/watch?v=qVITI34ZFuk&ab_channel=Grafana)
- [grafana/tns: Observability Demo App](https://github.com/grafana/tns)
- [Correlating Traces, Logs, and Metrics - OpenTelemetry NodeJS](https://signoz.io/opentelemetry/correlating-traces-logs-metrics-nodejs/)
- [3 models for logging with OpenTelemetry and Elastic](https://www.elastic.co/observability-labs/blog/3-models-logging-opentelemetry)
- [Opentelemetry 实践分享 - Golang 篇](https://cloud.tencent.com/developer/article/2318616)
- [OpenTelemetry docker-compose example](https://github.com/open-telemetry/opentelemetry-collector-contrib/blob/main/examples/demo/docker-compose.yaml)
- [New in Grafana 9.1: Trace to metrics allows users to navigate from a trace span to a selected data source](https://grafana.com/blog/2022/08/18/new-in-grafana-9.1-trace-to-metrics-allows-users-to-navigate-from-a-trace-span-to-a-selected-data-source/)
- [Leveraging OpenTelemetry and Grafana for observing, visualizing, and monitoring Kubernetes applications](https://grafana.com/blog/2024/11/22/leveraging-opentelemetry-and-grafana-for-observing-visualizing-and-monitoring-kubernetes-applications/?utm_source=grafana_news&utm_medium=rss)
- [使用 OpenTelemetry 和 Loki 实现高效的应用日志采集和分析](https://atbug.com/efficient-app-log-collection-analysis-opentelemetry-loki/)
- [Observability with OpenTelemetry Part 5 - Propagation and Baggage](https://trstringer.com/otel-part5-propagation/)
- [Observability Coordinated: Prometheus Exemplars (Metrics) — Grafana Tempo (Traces) — Loki (Logs)](https://www.youtube.com/watch?v=4YjaR-c0Cjc&ab_channel=LinhVu)
- [opentelemetry-collector-contrib/exporter/prometheusexporter](https://github.com/open-telemetry/opentelemetry-collector-contrib/tree/main/exporter/prometheusexporter)
- [Span profiles with Traces to profiles for Go](https://grafana.com/docs/pyroscope/latest/configure-client/trace-span-profiles/go-span-profiles/)
- [Span Profiles with Grafana Tempo and Pyroscope](https://github.com/grafana/pyroscope/blob/main/examples/tracing/tempo/README.md)