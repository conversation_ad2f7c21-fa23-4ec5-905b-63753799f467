---
title: AI
author: Se7en
categories:
 - AI
tags:
 - AI
 - GPU
---

## CUDA

CUDA 可以分为 Grid，Block 和 Thread 三个层次结构：

- 线程层次结构Ⅰ-Grid：kernel 在 device 上执行时，实际上是启动很多线程，一个 kernel 所启动的所有线程称为一个网格（grid），同一个网格上的线程共享相同的全局内存空间，grid 是线程结构的第一层次。
- 线程层次结构Ⅱ-Block：Grid 分为多个线程块（block），一个 block 里面包含很多线程，Block 之间并行执行，并且无法通信，也没有执行顺序，每个 block 包含共享内存（shared memory），可以共享里面的 Thread。
- 线程层次结Ⅲ-Thread：CUDA 并行程序实际上会被多个 threads 执行，多个 threads 会被群组成一个线程 block，同一个 block 中 threads 可以同步，也可以通过 shared memory 通信。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302346976.png)

因此 CUDA 和英伟达硬件架构有以下对应关系，从软件侧看到的是线程的执行，对应于硬件上的 CUDA Core，每个线程对应于 CUDA Core，软件方面线程数量是超配的，硬件上 CUDA Core 是固定数量的。Block 线程块只在一个 SM 上通过 Warp 进行调度，一旦在 SM 上调用了 Block 线程块，就会一直保留到执行完 kernel，SM 可以同时保存多个 Block 线程块，多个 SM 组成的 TPC 和 GPC 硬件实现了 GPU 并行计算。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302347553.png)

## 分布式并行

- **数据并行**是将整个数据集划分为多个子集，每个子集在不同的计算设备上进行处理。每个设备都使用相同的模型参数，但处理不同的数据部分。
- **张量并行**是将模型的参数或计算过程在多个设备上进行切分，允许在不同设备上并行处理模型的不同部分。这种方法特别适合于大型深度学习模型，尤其是当单个设备无法容纳整个模型时。
- **流水线并行**是一种将模型按层切分的方法，不同层放置在不同的计算节点上。数据流通过这些层，形成一个流水线，允许在一个 batch 结束前开始下一个 batch 的处理。



## 分布式训练

### 通信协调

#### 硬件

#### 软件

### 集合式通信方式

- 一对多：Scatter / Broadcast
- 多对一：Gather / Reduce
- 多对多：All-Reduce / All-Gather

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202411051026270.png)


https://juejin.cn/post/7063102006059237406#heading-3

### 分布式训练与模型算法关系

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410301920531.png)

### 分布式架构

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302131703.png)

## AI 集群

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302033413.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302032477.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410301939303.png)

### 为什么需要 AI 集群？

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302052602.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302053183.png)  

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302057787.png)

### AI 集群训练的关键指标

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302105850.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302108683.png)

### AI 集群的硬件组成

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302114930.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302114055.png)


## 数据存储

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302154838.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302156214.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302157018.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302158014.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302159069.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302201467.png)

### 大模型训练的存储优化方案

训练之前的存储优化

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302219909.png)

训练流程的存储优化

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302226519.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302228491.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302229066.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302236711.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302239875.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302241706.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410302243748.png)

## 推理

### 什么是推理？

推理，简单来说，就是在利用大量数据训练好模型的结构和参数后，使用小批量数据进行一次前向传播，从而得到模型输出的过程。在此过程中，并不涉及模型梯度和损失的优化。推理的最终目标，便是将训练好的模型部署到实际的生产环境中，使 AI 真正运行起来，服务于日常生活。

### 推理系统和推理引擎

**推理系统**，是一个专门用于部署神经网络模型，执行推理预测任务的 AI 系统。它类似于传统的 Web 服务或移动端应用系统，但专注于 AI 模型的部署与运行。推理系统会加载模型到内存，并进行版本管理，确保新版本能够顺利上线，旧版本能够安全回滚。此外，它还会对输入数据进行批量尺寸（Batch Size）的动态优化，以提高处理效率。通过提供服务接口（如 HTTP、gRPC 等），推理系统使得客户端能够方便地调用模型进行推理预测。

**推理引擎**，则是推理系统中的重要组成部分，它主要负责 AI 模型的加载与执行。推理引擎可分为调度与执行两层，聚焦于 Runtime 执行部分和 Kernel 算子内核层，为不同的硬件提供更加高效、快捷的执行引擎。它可以看作是一个基础软件，提供了一组 API，使得开发者能够在特定的加速器平台（如 CPU、GPU 和 TPU）上轻松地进行推理任务。目前市场上已有多种推理引擎，如字节跳动的 LightSeq、Meta AI 的 AITemplate、英伟达的 TensorRT，以及华为的 MindSpore Lite 和腾讯的 NCNN 等。

### 模型优化技术

#### 模型小型化

#### 离线优化压缩

离线优化压缩主要通过对轻量化或非轻量化模型应用剪枝、蒸馏、量化等压缩算法和手段，使模型体积更小、更轻便，从而提高执行效率。
常见的离线优化压缩技术有：

- 1.**低比特量化**是一种将模型权重和激活值从浮点数转换为低比特整数（如 8 位、4 位甚至更低）的技术。通过减少表示每个数值所需的比特数，可以显著减少模型的大小和内存占用，同时加速推理过程。然而，低比特量化也可能导致精度损失，因此需要在压缩率和精度之间找到平衡。
- 2.**二值化网络**是一种极端的量化方法，它将模型权重和激活值限制为两个可能的值（通常是+1 和-1）。这种方法可以进一步减小模型大小并提高推理速度，但可能导致更大的精度损失。因此，在设计二值化网络时，需要精心选择网络结构和训练策略，以在保持精度的同时实现高效的压缩。
- 3.**模型剪枝**通过移除网络中的冗余连接或神经元来减小模型大小。这可以通过设定阈值来删除权重较小的连接或神经元实现。剪枝后的模型不仅更小，而且往往具有更快的推理速度。然而，剪枝过程需要谨慎处理，以避免过度剪枝导致精度大幅下降。
- 4.**知识蒸馏**是一种将大型教师模型的知识转移到小型学生模型中的技术。通过让教师模型指导学生模型的学习过程，可以在保持较高精度的同时实现模型的小型化。这种方法的关键在于设计有效的蒸馏策略，以确保学生模型能够充分吸收教师模型的知识。

### 模型部署

推理系统一般可以部署在云或者边缘。云端部署的推理系统更像传统 Web 服务，在边缘侧部署的模型更像手机应用和 IOT 应用系统。两者有以下特点：

- 云（Cloud）端：云端有更大的算力，内存，且电更加充足满足模型的功耗需求，同时与训练平台连接更加紧密，更容易使用最新版本模型，同时安全和隐私更容易保证。相比边缘侧可以达到更高的推理吞吐量。但是用户的请求需要经过网络传输到数据中心并进行返回，同时使用的是服务提供商的软硬件资源。
- 边缘（Edge）端：边缘侧设备资源更紧张（例如，手机和 IOT 设备），且功耗受电池约束，需要更加在意资源的使用和执行的效率。用户的响应只需要在自身设备完成，且不需消耗服务提供商的资源。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202411041436067.png)



