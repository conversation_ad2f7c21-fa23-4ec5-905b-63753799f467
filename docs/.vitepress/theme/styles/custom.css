/* 灰度模式 */
/*html {
  filter: grayscale(95%);
  -webkit-filter: grayscale(95%);
  -moz-filter: grayscale(95%);
  -ms-filter: grayscale(95%);
  -o-filter: grayscale(95%);
  filter:progid:DXImageTransform.Microsoft.BasicImage(grayscale=.95);
}*/

body {
  font-size: 16px;
}

pre, code, kbd, samp {
  font-family: var(--vp-font-family-mono);
}

.vp-doc h2 {
  margin: 15px 0 0;
  padding-top: 10px;
  border-top: none;
}

.vp-doc h3,h4,h5 {
  margin: 10px 0 0;
}

.vp-doc h4 {
  font-size: 18px;
}

.VPDoc .content-container img {
  border-radius: 5px;
}

/* slim scroll bar for windows based web browsers - works on firefox */
* {
  scrollbar-color: var(--vp-c-gutter) var(--vp-button-alt-bg);
  scrollbar-width: thin;
}

/* slim scroll bar for windows based web browsers - works on chrome, edge, and safari */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* scrolling handle */
::-webkit-scrollbar-thumb {
  background: var(--vp-c-gutter);
}

/* handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: var(--vp-button-alt-hover-bg);
}

/* scrolling track/backgronund color */
::-webkit-scrollbar-track {
  background: var(--vp-button-alt-bg);
}

/* mathjax3.css */
mjx-container {
  display: inline-block;
  margin: auto 2px -2px;
}

mjx-container > svg {
  margin: auto;
}

.chinese-zodiac {
  display: inline-block;
  width: 20px;
  height: 20px;
  position: absolute;
  left: -10.5px;
  top: -1px;
  background: #fff;
  border: 1px solid #84b9e5;
  border-radius: 50%;
  cursor: pointer;
}

.text-color-red {
  --color-red: 254 45 70;
  --tw-text-opacity: 1;
  color: rgb(var(--color-red)/var(--tw-text-opacity));
}

.text-color-orange {
  --color-orange: 255 102 0;
  --tw-text-opacity: 1;
  color: rgb(var(--color-orange)/var(--tw-text-opacity));
}

.text-color-yellow {
  --color-yellow: 250 169 14;
  --tw-text-opacity: 1;
  color: rgb(var(--color-yellow)/var(--tw-text-opacity));
}

.text-color-gray {
  --color-gray: 145 149 163;
  --tw-text-opacity: 1;
  color: rgb(var(--color-gray)/var(--tw-text-opacity));
}

.mr-\[6px\] {
  margin-right: 6px;
}