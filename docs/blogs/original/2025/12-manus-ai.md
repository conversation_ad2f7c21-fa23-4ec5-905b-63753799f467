---
title: Manus 刷屏，全网求邀请码！这款 AI Agent 究竟有多强？
author: Se7en
date: 2025/03/06 21:30
categories:
 - AI
tags:
 - Manus
 - AI
---

# Manus 刷屏，全网求邀请码！这款 AI Agent 究竟有多强？

继 DeepSeek 爆火之后，科技圈今天又被 Manus 刷屏了！！！

北京时间 3 月 6 日凌晨 Monica 团队推出了 [Manus](https://manus.im/) -- 全球首款通用型 AI Agent。Manus 能够独立思考、规划和执行复杂任务，并直接交付完整的任务成果，而不是仅仅提供建议或答案。Manus 拥有强大的工具调用能力，能自主完成从任务规划到执行的全流程，如文件处理、数据分析、代码编写、内容创作等。Manus 的产品名，意思为“手”，来自拉丁文 "mens et manus"，象征着将知识从脑中转化为实际行动。

Manus 在 **GAIA 基准测试**（一项严格评估 AI 解决现实世界问题能力的测试）中取得了业界领先的成绩，充分证明了其能够交付可量化且可靠的结果。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202503062043319.png)


许多人可能对 **GAIA** 并不熟悉，因此这里简单介绍一下，为什么 Manus 让人感觉如此强大。  

**GAIA（General AI Assistants）** 是由 **Meta AI（FAIR）、Hugging Face** 等研究团队于 2023 年推出的基准测试系统，专门用于评估 AI 助手的能力。它通过一系列现实世界的问题，考察 AI 在 **推理、多模态处理、网页浏览、工具调用** 方面的能力。这些问题对人类而言相对简单，但对先进的 AI 仍然极具挑战性——人类平均得分 **92%**，而 **GPT-4（搭配插件）仅得 15%**。GAIA 设有三个难度等级，要想取得优异表现，AI 需要具备网络搜索、工具调用、编程和文件处理的能力。  

传统的 AI 评测通常侧重数学、专业知识或编程能力，而 GAIA 更加强调 AI 在现实场景中的实际问题解决能力，要求 AI 具备多步推理和综合应用能力。同样，Manus 通过提供强大的工具，使用户能够以直观的方式应对复杂问题，真正实现高效智能的辅助决策。

在官方发布的视频中，介绍了 3 个 Manus 在实际使用场景中所完成的工作案例：

第一个任务是简历筛选。从 15 份简历中筛选出适合强化学习算法工程师职位的人选，Manus 已经展现出了像人类实习生的一面，手动解压缩文件，并逐页浏览每一份简历，同时记录其中的重要信息。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202503061852922.png)

在 Manus 提供的结果中，不仅包含自动生成的排名建议，还会基于工作经验等关键维度，将候选人划分为不同等级。当用户表达希望以 Excel 表格形式查看数据时，Manus 还能自动生成并格式化相应的表格，以更直观、便捷的方式呈现筛选结果。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202503061854776.png)

第二个案例，是遴选房产。案例中用户希望在纽约购买房产，输入的要求是希望同时有安全的社区环境、低犯罪率，以及优质的中小学教育资源——当然还包括最重要的预算，足够在每月固定收入的情况下负担得起。

在这个需求中，Manus 将复杂任务分解为待办事项列表，包括研究安全社区、识别优质学校、计算预算、搜索房产等。并通过网络搜索，仔细阅读有关纽约最安全社区的文章，收集相关信息。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202503061902115.png)

Manus 通过编写一个 Python 程序，根据用户收入计算可负担的房产预算。结合房地产网站上相关的房价信息，根据预算范围筛选房产列表。Manus 甚至可以人类一样在网站上输入搜索条件进行搜索。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202503061858723.png)

最后，Manus 会整合所有收集的信息，撰写详细报告，包括社区安全分析、学校质量评估、预算分析、推荐房产列表以及相关资源链接——就像一个专业的房地产经纪人一样。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202503061904791.png)

在最后一个案例中，Manus 展示了对股票价格的分析能力。

案例给出的任务是分析过去三年中英伟达、迈威尔科技和台积电股票价格之间的相关性：众所周知这三支股票之间存在紧密的关联性，但对于新手用户来讲，很难快速将其中的因果关系整理清楚。

而 Manus 的操作，与一个真正的股票经纪人非常相似，它先是通过 API 访问雅虎金融等信息网站，来获取股票历史数据，同时还会交叉验证数据准确程度，避免被单一信息来源误导，对最终产生结果带来重大影响。

Manus 同样运用了 Python 编程，并结合专业的金融分析工具，对复杂的金融数据进行深入解析。最终，它生成了一份详尽的报告，清晰呈现数据背后的逻辑与趋势。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202503061906266.png)

甚至 Manus 还能创建一个在线网站，将分析结果以更加动态、直观的方式展示给用户。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202503061908623.png)

不仅如此，在 Manus 官网还展示了十多个 Manus 能够使用的场景：直接使用 Manus 帮你整理行程、个性化推荐旅游路线，还能让它学习使用各种复杂工具，来流程化的完成日常工作。

此外，在 X 上也有一些有内测资格的用户分享了 Manus 的使用体验，比如：

- **写一本自定义主题的小说**（session 回放链接：https://manus.im/share/Aa0M0gvOybi9cRwDK0RNpx?replay=1 ），Manus 洋洋洒洒写了 34 页 PDF。我读了一下，写得挺全面的，从虚拟机时代到容器时代，再到如今的新兴 WebAssembly。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202503062024387.png)

- **🤣 泽连斯基的白宫互动游戏**（session 回放链接：https://manus.im/share/Sc5a78DH57lQ5aYJbVAvZp?replay=1 ）

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202503062016314.png)

生成的游戏已经部署到了永久到公共 URL 上，可以点击进行体验：https://betedmvh.manus.space。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202503062018996.png)

还有一个**谷歌 CEO 的模拟游戏**（session 回放链接：https://manus.im/share/cmRIphYJybxNiLSkWn6PJn?replay=1 ）

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202503062021740.png)

可以通过以下链接访问游戏：https://wqvmzimt.manus.space

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202503062023184.png)

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202503062023904.png)

通过上面的例子，你会发现 Manus 和 ChatGPT、Claude 这类传统聊天机器人完全不同。它不仅能理解你的需求，还能主动执行任务并交付完整成果，而不是仅仅给你建议或答案。

在这个过程中，真正让 Manus 与传统工具拉开差距的，是它具备自主规划和执行任务的能力，确保不仅仅是“听懂”指令，而是能够独立完成任务。它不会停留在被动等待用户输入每一步操作，而是会主动拆解任务、调用适当的工具，并持续优化执行流程。  

更重要的是，Manus 具备自主学习能力，让它的成长逻辑更接近人类——尽管目前它可能还无法在某个领域达到专家级别的精通，但它已经展现出了巨大的潜力，能够不断积累经验、优化策略，越用越智能。这一点让 AI Agent 的泛用性大幅提升，例如在实际测试中，用户只需描述某个视频的内容，Manus 无需依赖传统搜索引擎，就能跨平台找到对应的抖音短视频链接，展现出远超一般 AI 工具的搜索与理解能力。  

此外，Manus 完全基于云端异步运行，它的能力不受设备算力或平台形态的限制。你甚至可以在下达指令后直接关掉电脑，而 Manus 仍然会在后台独立完成任务，并在结果生成后自动通知你。这种交互模式让 AI 真正融入日常工作流，就像你在下班后给实习生发微信：“文件整理好发我。” 只不过，这个实习生 7×24 小时在线，效率更高，还不用担心他哪天突然“整顿职场”。

## 总结

Manus 在发布后迅速引起了科技圈的关注，证明了其在推理、多模态处理、工具调用等方面的强大能力。目前，Manus 仍处于内测阶段，仅限邀请码使用。此时此刻，全网都在疯狂求邀请码。如果你也想抢先体验，不妨立即前往 Manus 官网 申请加入 waitlist，也许就能提前解锁这款划时代的 AI Agent。