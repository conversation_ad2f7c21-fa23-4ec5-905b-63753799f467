---
title: CPU
author: Se7en
categories:
  - Interview
tags:
  - Operating System
  - CPU
---

## 为什么要有 DMA 技术?

在没有 DMA 技术前，I/O 的过程是这样的：

- CPU 发出对应的指令给磁盘控制器，然后返回；
- 磁盘控制器收到指令后，于是就开始准备数据，会把数据放入磁盘控制器的内部缓冲区中，然后产生一个中断；
- CPU 收到中断信号后，停下手头的工作，接着把磁盘控制器的缓冲区的数据一次一个字节地读进自己的寄存器，然后再把寄存器里的数据写入到内存，而在数据传输期间，CPU 是无法执行其他任务的。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410231324143.png)

DMA（Direct Memory Access） 技术就是在进行 I/O 设备和内存的数据传输的时候，数据搬运的工作全部交给 DMA 控制器，而 CPU 不再参与任何与数据搬运相关的事情，这样 CPU 就可以去处理别的事务了。

DMA 的具体过程如下：

- 用户进程调用 read 方法，向操作系统发出 I/O 请求，请求读取数据到自己的内存缓冲区中，进程进入阻塞状态；
- 操作系统收到请求后，进一步将 I/O 请求发送给 DMA 控制器，然后让 CPU 执行其他任务；
- DMA 控制器将 I/O 请求发送给磁盘；
- 磁盘收到 DMA 的 I/O 请求后，把数据从磁盘读取到磁盘控制器的缓冲区中，当缓冲区被读满后，向 DMA 控制器发起中断信号，告知自己的缓冲区已满；
- DMA 控制器收到磁盘的信号，将磁盘控制器缓冲区中的数据拷贝到内核缓冲区中，此时不占用 CPU，CPU 可以执行其他任务。
- 当 DMA 控制器读取了足够多的数据，就会发送中断信号给 CPU；
- CPU 收到 DMA 控制器的信号，知道数据已经准备好了，于是将数据从内核拷贝到用户空间，返回系统调用。

可以看到，CPU 不再参与将数据从磁盘控制器的缓冲区搬运到内核空间的工作，这部分工作全程由 DMA 完成。但是 CPU 在这个过程中也是必不可少的，因为传输什么数据，从哪里传输到哪里，都需要 CPU 来告诉 DMA 控制器。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410231330513.png)

参考资料：

- [9.1 什么是零拷贝？](https://xiaolincoding.com/os/8_network_system/zero_copy.html#%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E6%9C%89-dma-%E6%8A%80%E6%9C%AF)

## 如何实现零拷贝？

要搞明白零拷贝技术就要先搞清楚传统 I/O 的执行流程，传统的 I/O 的执行流程如下，期间共发生了 4 次用户态与内核态的上下文切换，因为发生了 2 次系统调用，一次是 read()，一次是 write()，每次系统调用都得先从用户态切换到内核态，等内核任务完成后，再从内核态切换回用户态。
期间还发生了 4 次数据拷贝，其中两次是 DMA 的拷贝，另外两次则是通过 CPU 拷贝的。
可以看到我们只是处理一份数据，结果却搬运了 4 次，过多的数据拷贝无疑会消耗大量的 CPU 资源，降低系统的性能。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410231339848.png)

所谓的零拷贝技术不是指不发生拷贝，而是在用户态没有进行拷贝。实现零拷贝的技术的方式通常有 2 种：

- **mmap（memory map）**：`mmap()` 系统调用函数会直接把内核缓冲区里的数据映射到用户空间，这样操作系统内核与用户空间共享同一块内存区域，这样就避免了数据的拷贝。具体过程如下：

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410231345331.png)

- **sendfile**：`sendfile()` 可以替代 `read()` 和 `write` 这 2 个系统调用，这样就可以减少一次系统调用，也就减少了 2 次上下文切换的开销。其次，`sendfile()` 函数可以直接把内核缓冲区的数据拷贝到 Socket 缓冲区，不再拷贝到用户态，这样就只有 2 次上下文切换和 3 次数据拷贝。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410231351414.png)

- 但是这还不是真正的零拷贝技术，如果网卡支持 SG-DMA 技术，我们可以进一步减少通过 CPU 把内核缓冲区里的数据拷贝到 Socket 缓冲区里的过程。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202410231352570.png)

传统方式以及不同零拷贝技术的对比如下：

| 操作方式              | CPU 拷贝（次数） | DMA 拷贝（次数） | 上下文切换（次数） |
|-------------------|------------| ---------------- | ------------------ |
| 传统 I/O            | 2          | 2                | 4                  |
| mmap              | 1          | 2                | 4                  |
| sendfile          | 1          | 2                | 2                  |
| sendfile (SG-DMA) | 0          | 2                | 2                  |

参考资料：

- [面试官问：kafka 为什么如此之快？](https://xie.infoq.cn/article/51b6764c48ff70988e124a868)
- [9.1 什么是零拷贝？](https://xiaolincoding.com/os/8_network_system/zero_copy.html#%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E6%9C%89-dma-%E6%8A%80%E6%9C%AF)
- [说一下零拷贝的实现原理？](https://www.javacn.site/interview/basic/zero-copy.html)

## 如何理解平均负载？

平均负载是指单位时间内，系统处于可运行状态和不可中断状态的平均进程数，也就是平均活跃进程数，它和 CPU 使用率并没有直接关系。

既然平均的是活跃进程数，那么最理想的，就是每个 CPU 上都刚好运行着一个进程，这样每个 CPU 都得到了充分利用。比如当平均负载为 2 时，意味着什么呢？

- 在只有 2 个 CPU 的系统上，意味着所有的 CPU 都刚好被完全占用。
- 在 4 个 CPU 的系统上，意味着 CPU 有 50% 的空闲。
- 而在只有 1 个 CPU 的系统中，则意味着有一半的进程竞争不到 CPU。

平均负载高有可能是 CPU 密集型进程导致的；平均负载高并不一定代表 CPU 使用率高，还有可能是 I/O 更繁忙了；当发现负载高的时候，你可以使用 mpstat、pidstat 等工具，辅助分析负载的来源。
