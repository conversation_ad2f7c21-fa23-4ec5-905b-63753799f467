/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ACard: typeof import('@arco-design/web-vue')['Card']
    ACol: typeof import('@arco-design/web-vue')['Col']
    AList: typeof import('@arco-design/web-vue')['List']
    AListItem: typeof import('@arco-design/web-vue')['ListItem']
    Archive: typeof import('./.vitepress/theme/components/Archive.vue')['default']
    ARow: typeof import('@arco-design/web-vue')['Row']
    ArticleMetadata: typeof import('./.vitepress/theme/components/ArticleMetadata.vue')['default']
    ATag: typeof import('@arco-design/web-vue')['Tag']
    Comment: typeof import('./.vitepress/theme/components/layout/Comment.vue')['default']
    Copyright: typeof import('./.vitepress/theme/components/layout/Copyright.vue')['default']
    DirectoryList: typeof import('./.vitepress/theme/components/DirectoryList.vue')['default']
    Footer: typeof import('./.vitepress/theme/components/layout/Footer.vue')['default']
    IconShareAlt: typeof import('@arco-design/web-vue/es/icon')['IconShareAlt']
    IconTrophy: typeof import('@arco-design/web-vue/es/icon')['IconTrophy']
    Tag: typeof import('./.vitepress/theme/components/Tag.vue')['default']
    WordCloud: typeof import('./.vitepress/theme/components/WordCloud.vue')['default']
  }
}
