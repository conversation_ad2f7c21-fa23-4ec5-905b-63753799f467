
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="shortcut icon" href="https://www.rapid7.com/includes/img/favicon.ico" />
  <title>[Rapid7 | InsightCloudSec] Kubernetes RBAC Power Toys</title>

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css?family=Poppins:200,300,400,600,700,800" rel="stylesheet">

  <!-- Icons -->
  <link href="https://use.fontawesome.com/releases/v5.0.6/css/all.css" rel="stylesheet">

  <!-- Argon CSS -->
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
</head>
	<body>
		<script src="https://d3js.org/d3.v5.min.js"></script>
		<script src="https://unpkg.com/viz.js@1.8.1/viz.js" type="application/javascript/"></script>
		<script src="https://unpkg.com/d3-graphviz@2.6.1/build/d3-graphviz.js"></script>
		
		<!-- Core -->
		<script src="https://code.jquery.com/jquery-3.4.1.slim.min.js" integrity="sha384-J6qa4849blE2+poT4WnyKhv5vZF5SrPo0iEjwBvKU7imGFAV0wwj1yYfoRSJoZ+n" crossorigin="anonymous"></script>
		<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
		<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js" integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6" crossorigin="anonymous"></script>
		<!-- script src="/assets/js/argon-design-system.min.js"></script -->

		
		<nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4">
			<div class="row">
				<div class="col-6 collapse-brand">
					<a href="javascript:void(0)">
						<img src="https://www.rapid7.com/includes/img/Rapid7_logo.svg" height="24">
					</a>
				</div>
				<div class="col-6 collapse-close">
					<button type="button" class="navbar-toggler" data-toggle="collapse" data-target="#navbar-default" aria-controls="navbar-default" aria-expanded="false" aria-label="Toggle navigation">
						<span></span>
						<span></span>
					</button>
				</div>
			</div>
			<div class="container">
				<a class="navbar-brand" href="#"><img src="https://www.rapid7.com/globalassets/_logos/insightcloudsec-w.svg" height="28"><span style="font-weight: 100; font-style: normal; font-size: 22px;"> | RBAC Tool</span></a>
				<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar-default" aria-controls="navbar-default" aria-expanded="false" aria-label="Toggle navigation">
					<span class="navbar-toggler-icon"></span>
				</button>
				<div class="collapse navbar-collapse" id="navbar-default">
					<div class="navbar-collapse-header">
						<div class="row">
							<div class="col-6 collapse-brand">
							</div>
							<div class="col-6 collapse-close">
								<button type="button" class="navbar-toggler" data-toggle="collapse" data-target="#navbar-default" aria-controls="navbar-default" aria-expanded="false" aria-label="Toggle navigation">
									<span></span>
									<span></span>
								</button>
							</div>
						</div>
					</div>

					<ul class="navbar-nav ml-auto">
						<li class="nav-item">
							<a class="nav-link nav-link-icon" href="https://github.com/alcideio/rbac-tool" target="_blank">
								<i class="fab fa-github"></i>
								<span class="nav-link-inner--text">GitHub</span>
							</a>
						</li>
						<li class="nav-item">
						  <a class="nav-link nav-link-icon" href="https://www.rapid7.com/products/insightcloudsec/" target="_blank">
							<i class="fas fa-home"></i>
							<span class="nav-link-inner--text">Site</span>
						  </a>
						</li>
						<li class="nav-item">
							<a class="nav-link nav-link-icon" href="https://codelab.alcide.io" target="_blank">
								<i class="fas fa-code"></i>
								<span class="nav-link-inner--text">Codelabs</span>
							</a>
						</li>
						<li class="nav-item">
						  <a class="nav-link nav-link-icon" href="https://twitter.com/rapid7" target="_blank">
							<i class="fab fa-twitter"></i>
							<span class="nav-link-inner--text">Twitter</span>
						  </a>
						</li>
				  </ul>
				</div>
			</div>
		</nav>


		
		<div class="container-fluid">
			<div class="row">
				<div class="col-3">
					<div class="card border-light"">

					  <ul class="list-group list-group-flush">
						<div><li class="list-group-item border-ligh">
<style>
  #legend svg {
    height: auto;
    width: 100%;
  }
</style>
<div id="legend" style="text-align: center;">
</div>

<script>
var dotSrclegend = `digraph  {
	subgraph cluster_s1 {
		subgraph cluster_s2 {
			label="Namespace";style="rounded,dashed";
			n4[color="#e33a1f",fillcolor="#1b60db",fontcolor="#030303",fontname="Poppins 100 normal",label="Missing Subject\n(Kind)",margin="0.22,0.11",penwidth="2.0",shape="box",style="dotted"];
			n3[color="#01040a",fillcolor="#1b60db",fontcolor="white",fontname="Poppins 100 normal",label="Subject\n(Kind)",margin="0.22,0.11",penwidth="1.0",shape="box",style="filled"];
			n6[color="#01080a",fillcolor="#006666",fontcolor="#f4f4f4",fontname="Poppins 100 normal",label="ClusterRole",penwidth="1.0",shape="oval",style="filled,dashed"];
			n5[color="#01080a",fillcolor="#17b87e",fontcolor="white",fontname="Poppins 100 normal",label="Role",penwidth="1.0",shape="oval",style="filled"];
			n8[color="#01080a",fillcolor="#00994c",fontcolor="white",fontname="Poppins 100 normal",label="RoleBinding",penwidth="1.0",shape="oval",style="filled"];
			n9[color="#01080a",fillcolor="#00994c",fontcolor="white",fontname="Poppins 100 normal",label="RoleBinding",penwidth="1.0",shape="oval",style="filled"];
			n12[fillcolor="#DCDCDC",fontsize="10",label=<Namespace-scoped access rules From ClusterRole>,penwidth="1.0",shape="note"];
			n11[fillcolor="#DCDCDC",fontsize="10",label=<Namespace-scoped access rules>,penwidth="1.0",shape="note"];
			n4->n8[dir="back"];
			n3->n8[dir="back"];
			n3->n9[dir="back"];
			n6->n12;
			n5->n11;
			n8->n5;
			n9->n6;
			
		}
		label="Legend";style="invis";
		n7[color="#01080a",fillcolor="#006666",fontcolor="#f4f4f4",fontname="Poppins 100 normal",label="ClusterRole",penwidth="1.0",shape="oval",style="filled"];
		n10[color="#01080a",fillcolor="#006633",fontcolor="#f4f4f4",fontname="Poppins 100 normal",label="ClusterRoleBinding",penwidth="1.0",shape="oval",style="filled"];
		n14[color="#01080a",fillcolor="#ffbf00",fontcolor="black",fontname="Poppins 100 normal",label="PSP",penwidth="1.0",shape="note",style="filled"];
		n13[fillcolor="#DCDCDC",fontsize="10",label=<Cluster-scoped access rules>,penwidth="1.0",shape="note"];
		n15[fillcolor="#DCDCDC",fontsize="10",label=<Pod Security Policy>,penwidth="1.0",shape="note"];
		n7->n13;
		n10->n7;
		n14->n15;
		
	}
	fontname="Poppins 100 normal";fontsize="12.00";newrank="true";
	n3->n10[dir="back"];
	n12->n14;
	{rank=same; n5;n6;n7;};
	{rank=same; n14;};
	
}
`;

var graphvizlegend = d3.select("#legend").graphviz()
    .transition(function () {
        return d3.transition("main")
            .ease(d3.easeLinear)
            .delay(500)
            .duration(1500);
    })
    .logEvents(true)
    .on("initEnd", renderlegend);

function renderlegend() {
    console.log('DOT source =', dotSrclegend);
    dotSrcLines = dotSrclegend.split('\n');

    graphvizlegend
        .transition(function() {
            return d3.transition()
                .delay(100)
                .duration(1000);
        })
        .renderDot(dotSrclegend)
		.zoom(true);
}

</script>
</li></div>
					  </ul>
					  <div class="bg-light p-1 text-center">
						Legend
					  </div>
					</div>
				</div>
				<div class="col-6">
					
<style>
  #rbacgraph svg {
    height: auto;
    width: auto;
  }
</style>
<div id="rbacgraph" style="text-align: center;">
</div>

<script>
var dotSrcrbacgraph = `digraph  {
	fontname="Poppins 100 normal";fontsize="12.00";newrank="true";
	
}
`;

var graphvizrbacgraph = d3.select("#rbacgraph").graphviz()
    .transition(function () {
        return d3.transition("main")
            .ease(d3.easeLinear)
            .delay(500)
            .duration(1500);
    })
    .logEvents(true)
    .on("initEnd", renderrbacgraph);

function renderrbacgraph() {
    console.log('DOT source =', dotSrcrbacgraph);
    dotSrcLines = dotSrcrbacgraph.split('\n');

    graphvizrbacgraph
        .transition(function() {
            return d3.transition()
                .delay(100)
                .duration(1000);
        })
        .renderDot(dotSrcrbacgraph)
		.zoom(true);
}

</script>

				</div>
				<div class="col-3">
				</div>
			</div>
		</div>


		
		<div class="row p20 mt-4">
					<div class="col-md-4 text-center mt-4">
						</div>
					<div class="col-md-4 text-center p10">
						<div>
 							<span>
								<a href="javascript:void(0)"><img src="https://www.rapid7.com/contentassets/cd8848ed7b7d4ab1818066b23f0d6d0c/r7-insight-wheel2.png" height="256"></a>
								<p class="mt-4" style="font-weight: 100; font-style: normal; font-size: 18px;">Brought to You by <a target="_blank" href="https://www.rapid7.com/products/insightcloudsec/">Rapid7 InsightCloudSec</a> Kubernetes Obsession</p>
							</span>
						</div>
					</div>
					<div class="col-md-4 text-center">
					</div>
		</div>


	</body>
</html>
  